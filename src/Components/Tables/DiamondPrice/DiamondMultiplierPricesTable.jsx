import React, { useState, useEffect } from "react";
import ActionButton from "../../Button/ActionButton";
import ConfirmModal from "../../Modal/ConfirmModal";
import MessageNotification from "../../MessageNotification/MessageNotification";

export default function DiamondMultiplier({ data, onUpdate, onAddNewRow }) {
  const [isEditing, setIsEditing] = useState(false);
  const [editableData, setEditableData] = useState([]);
  const [buttonLoader, setButtonLoader] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [error, setError] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [showAddRow, setShowAddRow] = useState(false);
  const [newRowData, setNewRowData] = useState({
    diamondType: "",
    profitMultiplier: ""
  });
  const [validationErrors, setValidationErrors] = useState({});

  useEffect(() => {
    setEditableData([...data]);
  }, [data]);

  const handleEditClick = () => {
    isEditing ? setModalOpen(true) : setIsEditing(true);
  };

  const handleMultiplierChange = (id, value) => {
    // Handle empty string case
    if (value === '') {
      setValidationErrors(prev => ({
        ...prev,
        [id]: true // Empty values are invalid
      }));
      setEditableData((prevData) =>
        prevData.map((item) =>
          item._id === id
            ? { ...item, profitMultiplier: '' }
            : item
        )
      );
      return;
    }

    const numValue = parseFloat(value);

    // Validate the value
    let hasError = false;
    if (isNaN(numValue) || numValue < 0 || numValue > 100) {
      hasError = true;
    }

    setValidationErrors(prev => ({
      ...prev,
      [id]: hasError
    }));

    setEditableData((prevData) =>
      prevData.map((item) =>
        item._id === id
          ? { ...item, profitMultiplier: isNaN(numValue) ? value : numValue }
          : item
      )
    );
  };

  const handleDiamondTypeChange = (id, value) => {
    setEditableData((prevData) =>
      prevData.map((item) =>
        item._id === id ? { ...item, diamondType: value } : item
      )
    );
  };

  const handleConfirmChanges = () => {
    // Check for validation errors before saving
    const hasErrors = Object.values(validationErrors).some(error => error);
    if (hasErrors) {
      setError("Acceptable inputs is between 0 to 100");
      setTimeout(() => setError(null), 5000);
      return;
    }

    setModalOpen(false);
    setButtonLoader(true);

    const updatedData = editableData
      .filter((item) => {
        const originalItem = data.find((d) => d._id === item._id);
        return (
          originalItem &&
          originalItem.profitMultiplier !== item.profitMultiplier
        );
      })
      .map((item) => ({
        id: item._id,
        profitMultiplier: item.profitMultiplier,
      }));

    if (updatedData.length > 0) {
      onUpdate(updatedData);
    }

    setIsEditing(false);
    setButtonLoader(false);
  };

  const handleCancelChanges = () => {
    setEditableData([...data]);
    setValidationErrors({});
    setIsEditing(false);
    setModalOpen(false);
  };

  const handleNewRowDataChange = (field, value) => {
    // Validate profitMultiplier field
    if (field === "profitMultiplier") {
      // Handle empty string case
      if (value === '') {
        setValidationErrors(prev => ({
          ...prev,
          newRow: true // Empty values are invalid
        }));
        setNewRowData(prev => ({
          ...prev,
          [field]: ''
        }));
        return;
      }

      const numValue = parseFloat(value);

      // Validate the value
      let hasError = false;
      if (isNaN(numValue) || numValue < 0 || numValue > 100) {
        hasError = true;
      }

      setValidationErrors(prev => ({
        ...prev,
        newRow: hasError
      }));

      setNewRowData(prev => ({
        ...prev,
        [field]: isNaN(numValue) ? value : numValue
      }));
      return;
    }

    setNewRowData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddRow = async () => {
    if (!newRowData.diamondType || !newRowData.profitMultiplier) {
      setError("Please fill in all fields");
      setTimeout(() => setError(null), 5000);
      return;
    }

    // Check for validation errors
    if (validationErrors.newRow) {
      setError("Acceptable inputs is between 0 to 100");
      setTimeout(() => setError(null), 5000);
      return;
    }

    const newRow = {
      _id: Date.now().toString(),
      diamondType: newRowData.diamondType,
      profitMultiplier: parseFloat(newRowData.profitMultiplier),
    };

    try {
      setEditableData((prevData) => [...prevData, newRow]);

      if (onAddNewRow) {
        await onAddNewRow(newRow);
      }

      // Reset form
      setNewRowData({
        diamondType: "",
        profitMultiplier: ""
      });
      setShowAddRow(false);
    } catch (error) {
      console.error("Error adding row:", error);
      setError("Error adding row");
      setTimeout(() => setError(null), 5000);
    }
  };

  const handleCancelAddRow = () => {
    setNewRowData({
      diamondType: "",
      profitMultiplier: ""
    });
    setShowAddRow(false);
  };

  return (
    <div className="px-5 pt-2 pb-8">
      {successMessage && (
        <MessageNotification type="success" message={successMessage} />
      )}
      {error && (
        <MessageNotification
          type="error"
          message="Error updating profit multipliers"
        />
      )}

      <div className="flex justify-between items-center mt-6 mb-6">
        <h3 className="font-bold text-gray-800" style={{ fontSize: '26px' }}>
          Profit Multiplier
        </h3>
        <ActionButton
          onClick={handleEditClick}
          onDiscard={handleCancelChanges}
          isLoading={buttonLoader}
          isEditing={isEditing}
          text="Profit Multipliers"
        />
      </div>

      {/* Table Container */}
      <div className="flex justify-center">
        <div className="overflow-x-auto border border-gray-300 rounded-lg shadow-md min-w-[50%]">
          <table className="min-w-full divide-y divide-gray-300">
          <thead className="bg-gray-100">
            <tr>
              <th className="px-6 py-3 text-center bg-gray-200">
                Diamond Color-Clarity
              </th>
              <th className="px-6 py-3 text-center bg-gray-200">
                Diamond Price Profit Multiplier
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 bg-yellow-100 text-center">
            {editableData.map((item) => (
              <tr key={item._id}>
                <td className="p-4 font-semibold text-center bg-gray-50">
                  {item.diamondType}
                </td>
                <td className="p-4 text-center">
                  {isEditing ? (
                    <input
                      type="number"
                      className={`w-20 p-2 border rounded-md text-center ${
                        validationErrors[item._id] ? 'border-red-500 bg-red-50' : 'border-gray-300'
                      }`}
                      value={
                        item.profitMultiplier !== ""
                          ? item.profitMultiplier
                          : ""
                      }
                      onChange={(e) =>
                        handleMultiplierChange(item._id, e.target.value)
                      }
                    />
                  ) : (
                    `${item.profitMultiplier}`
                  )}
                </td>
              </tr>
            ))}

            {/* Add New Row Form within table */}
            {showAddRow && (
              <tr className="border-2 border-gray-300">
                <td className="p-4 text-center bg-gray-50">
                  <input
                    type="text"
                    placeholder="Diamond Color-Clarity"
                    value={newRowData.diamondType}
                    onChange={(e) => handleNewRowDataChange("diamondType", e.target.value)}
                    className="w-full p-2 border rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </td>
                <td className="p-4 text-center bg-yellow-100">
                  <input
                    type="number"
                    placeholder="Profit Multiplier"
                    value={newRowData.profitMultiplier}
                    onChange={(e) => handleNewRowDataChange("profitMultiplier", e.target.value)}
                    className={`w-20 p-2 border rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      validationErrors.newRow ? 'border-red-500 bg-red-50' : 'border-gray-300'
                    }`}
                  />
                </td>
              </tr>
            )}

            {/* Action buttons row when adding */}
            {showAddRow && (
              <tr className="bg-gray-50">
                <td colSpan="2" className="p-4 text-center">
                  <div className="flex justify-center gap-2">
                    <button
                      onClick={handleAddRow}
                      className="px-4 py-2 bg-green-600 cursor-pointer text-white rounded-md hover:bg-green-700 transition"
                    >
                      Add Row
                    </button>
                    <button
                      onClick={handleCancelAddRow}
                      className="px-4 py-2 bg-red-600 cursor-pointer text-white rounded-md hover:bg-red-700 transition"
                    >
                      Cancel
                    </button>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
          </table>
        </div>
      </div>

      {/* Centered Add Row Button Below the Table */}
      {!showAddRow && (
        <div className="flex justify-center mt-4">
          <button
            onClick={() => setShowAddRow(true)}
            className="cursor-pointer px-4 py-2 border-gray-400 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 transition"
          >
            Add New Row
          </button>
        </div>
      )}

      {/* Confirmation Modal */}
      {modalOpen && (
        <ConfirmModal
          isOpen={modalOpen}
          onClose={() => setModalOpen(false)}
          onConfirm={handleConfirmChanges}
          title="Confirm Changes"
          message="Are you sure you want to update profit multipliers?"
        />
      )}
    </div>
  );
}
