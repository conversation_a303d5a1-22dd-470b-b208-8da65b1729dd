import React, { useState, useEffect } from "react";
import ActionButton from "../../Button/ActionButton";

import MessageNotification from "../../MessageNotification/MessageNotification";
import AddColumn from "../../EditTables/AddColumn";

export default function ManufacturingRate({
  heading,
  data,
  onUpdate,
  onAddNewRow,
  onAddNewColumn,
  priceType = "manufacturingPrice",
  hideEditButton,
  hideAddNewRow,
}) {
  const [isEditing, setIsEditing] = useState(false);
  const [editableData, setEditableData] = useState([]);
  const [buttonLoader, setButtonLoader] = useState(false);

  const [successMessage, setSuccessMessage] = useState(null);
  const [errorMessage, setErrorMessage] = useState(null);
  const [showAddRow, setShowAddRow] = useState(false);
  const [showAddColumn, setShowAddColumn] = useState(false);
  const [sieveSizes, setSieveSizes] = useState([]);
  const [newRowData, setNewRowData] = useState({
    diamondType: "",
    prices: {}
  });
  const [validationErrors, setValidationErrors] = useState({});

  useEffect(() => {
    setEditableData([...data]);
    const uniqueSizes = [
      ...new Set(data.map((item) => `${item.minSize}-${item.maxSize}`)),
    ];
    setSieveSizes(uniqueSizes);

    // Initialize prices for new row when sieve sizes change
    const initialPrices = {};
    uniqueSizes.forEach(size => {
      initialPrices[size] = "";
    });
    setNewRowData(prev => ({
      ...prev,
      prices: initialPrices
    }));
  }, [data]);

  const diamondTypes = Array.from(
    new Set(editableData.map((item) => item.diamondType))
  );

  const handleEditClick = () => {
    if (!isEditing) {
      setIsEditing(true);
    } else {
      handleSaveChanges();
    }
  };

  const handlePriceChange = (type, size, value) => {
    // Validate for negative values
    const numValue = parseFloat(value);
    const key = `${type}_${size}`;

    // Validate the value
    let hasError = false;
    if (value !== '' && (isNaN(numValue) || numValue < 0)) {
      hasError = true;
    }

    setValidationErrors(prev => ({
      ...prev,
      [key]: hasError
    }));

    setEditableData((prevData) =>
      prevData.map((item) =>
        item.diamondType === type && `${item.minSize}-${item.maxSize}` === size
          ? { ...item, [priceType]: parseFloat(value) || 0 }
          : item
      )
    );
  };

  const handleSaveChanges = async () => {
    // Check for validation errors before saving
    const hasErrors = Object.values(validationErrors).some(error => error);
    if (hasErrors) {
      setErrorMessage("Negative values are not allowed");
      setTimeout(() => setErrorMessage(null), 5000);
      return;
    }

    setButtonLoader(true);

    const updatedPrices = editableData.map((item) => ({
      id: item._id,
      [priceType]: item[priceType],
    }));

    try {
      setIsEditing(false);
      setSuccessMessage("Price updated successfully!");
      onUpdate(updatedPrices);
    } catch (error) {
      console.error("Error updating prices:", error);
      setErrorMessage("Error updating prices");
    } finally {
      setButtonLoader(false);

      setTimeout(() => {
        setSuccessMessage(null);
        setErrorMessage(null);
      }, 5000);
    }
  };

  const handleCancelChanges = () => {
    setEditableData([...data]);
    setValidationErrors({});
    setIsEditing(false);
    setModalOpen(false);
  };

  const handleNewRowDataChange = (field, value) => {
    setNewRowData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNewRowPriceChange = (size, value) => {
    // Validate for negative values
    const numValue = parseFloat(value);
    const key = `newRow_${size}`;

    // Validate the value
    let hasError = false;
    if (value !== '' && (isNaN(numValue) || numValue < 0)) {
      hasError = true;
    }

    setValidationErrors(prev => ({
      ...prev,
      [key]: hasError
    }));

    setNewRowData(prev => ({
      ...prev,
      prices: {
        ...prev.prices,
        [size]: value
      }
    }));
  };

  const handleAddRow = async () => {
    if (!newRowData.diamondType.trim()) {
      setErrorMessage("Please enter a diamond type");
      setTimeout(() => setErrorMessage(null), 5000);
      return;
    }

    // Check for validation errors in new row prices
    const newRowErrors = Object.keys(validationErrors).filter(key => key.startsWith('newRow_'));
    if (newRowErrors.some(key => validationErrors[key])) {
      setErrorMessage("Negative values are not allowed");
      setTimeout(() => setErrorMessage(null), 5000);
      return;
    }

    const updatedRows = sieveSizes.map((size) => {
      const [minSize, maxSize] = size.split('-').map(Number);
      return {
        diamondType: newRowData.diamondType,
        minSize,
        maxSize,
        manufacturingPrice: parseFloat(newRowData.prices[size]) || 0,
      };
    });

    try {
      await onAddNewRow(updatedRows);
      // Reset form
      setNewRowData({
        diamondType: "",
        prices: sieveSizes.reduce((acc, size) => ({ ...acc, [size]: "" }), {})
      });
      setShowAddRow(false);
    } catch (error) {
      console.error("Error adding row:", error);
      const errorMsg = error.response?.data?.errors?.[0]?.message || "Error adding row!";
      setErrorMessage(errorMsg);
      setTimeout(() => setErrorMessage(null), 5000);
    }
  };

  const handleCancelAddRow = () => {
    setNewRowData({
      diamondType: "",
      prices: sieveSizes.reduce((acc, size) => ({ ...acc, [size]: "" }), {})
    });
    setShowAddRow(false);
  };

  const handleAddColumn = async (newColumn) => {
    const { minSize, maxSize, prices } = newColumn;

    const newData = diamondTypes.map((diamondType) => ({
      diamondType,
      minSize,
      maxSize,
      [priceType]: prices[diamondType] || 0, // Store price for each diamond type
    }));

    try {
      await onAddNewColumn(newData);
    } catch (error) {
      console.error("Error adding column:", error);
      const errorMsg = error.response?.data?.errors?.[0]?.message || "Error adding column!";
      setErrorMessage(errorMsg);
      setTimeout(() => setErrorMessage(null), 5000);
    }

    setShowAddColumn(false);
  };

  return (
    <div className="px-5 pt-2 pb-8">
      {successMessage && (
        <MessageNotification type="success" message={successMessage} />
      )}
      {errorMessage && (
        <MessageNotification type="error" message={errorMessage} />
      )}


      {heading && (
        <div className="flex justify-between items-center mt-6 mb-6">
          <h3 className="font-bold text-gray-800" style={{ fontSize: '26px' }}>
            {heading} (in rupees)
          </h3>
          {!hideEditButton && (
            <ActionButton
              onClick={handleEditClick}
              onDiscard={handleCancelChanges}
              isLoading={buttonLoader}
              isEditing={isEditing}
              text="Prices"
            />
          )}
        </div>
      )}

      <div className="overflow-x-auto border border-gray-300 rounded-lg shadow-md">
        <table className="min-w-full divide-y divide-gray-300">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-6 py-3 text-center bg-gray-200 align-middle whitespace-nowrap">Sieve Sizes</th>
            {sieveSizes.map((size) => (
              <th key={size} className="px-6 py-3 text-center bg-gray-200 align-middle whitespace-nowrap">
                {size}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-yellow-100 text-center">
          {diamondTypes.map((type, index) => (
            <tr key={`${type}-${index}`}>
              <td className="p-4 font-semibold text-center bg-gray-50">
                {type}
              </td>
              {sieveSizes.map((size) => (
                <td key={`${type}-${size}`} className="p-4 text-center">
                  {isEditing ? (
                    <input
                      type="number"
                      className={`w-20 p-2 border rounded-md text-center ${
                        validationErrors[`${type}_${size}`] ? 'border-red-500 bg-red-50' : 'border-gray-300'
                      }`}
                      value={
                        editableData.find(
                          (item) =>
                            item.diamondType === type &&
                            `${item.minSize}-${item.maxSize}` === size
                        )?.[priceType] || ""
                      }
                      onChange={(e) =>
                        handlePriceChange(type, size, e.target.value)
                      }
                    />
                  ) : (
                    `${
                      editableData
                        .find(
                          (item) =>
                            item.diamondType === type &&
                            `${item.minSize}-${item.maxSize}` === size
                        )
                        ?.[priceType]?.toLocaleString() || "-"
                    }`
                  )}
                </td>
              ))}
            </tr>
          ))}

          {/* Add New Row Form within table */}
          {showAddRow && (
            <tr className="border-2 border-gray-300">
              <td className="p-4 text-center bg-gray-50">
                <input
                  type="text"
                  placeholder="Sieve Size"
                  value={newRowData.diamondType}
                  onChange={(e) => handleNewRowDataChange("diamondType", e.target.value)}
                  className="w-full p-2 border rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </td>
              {sieveSizes.map((size) => (
                <td key={`new-${size}`} className="p-4 text-center bg-yellow-100">
                  <input
                    type="number"
                    placeholder="Price"
                    value={newRowData.prices[size] || ""}
                    onChange={(e) => handleNewRowPriceChange(size, e.target.value)}
                    className={`w-20 p-2 border rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      validationErrors[`newRow_${size}`] ? 'border-red-500 bg-red-50' : 'border-gray-300'
                    }`}
                  />
                </td>
              ))}
            </tr>
          )}

          {/* Action buttons row when adding */}
          {showAddRow && (
            <tr className="bg-gray-50">
              <td colSpan={sieveSizes.length + 1} className="p-4 text-center">
                <div className="flex justify-center gap-2">
                  <button
                    onClick={handleAddRow}
                    className="px-4 py-2 bg-green-600 cursor-pointer text-white rounded-md hover:bg-green-700 transition"
                  >
                    Add Row
                  </button>
                  <button
                    onClick={handleCancelAddRow}
                    className="px-4 py-2 bg-red-600 cursor-pointer text-white rounded-md hover:bg-red-700 transition"
                  >
                    Cancel
                  </button>
                </div>
              </td>
            </tr>
          )}
        </tbody>
        </table>
      </div>

      {!hideAddNewRow && (
        <div className="flex justify-center gap-4 mt-4">
          {!showAddColumn && !showAddRow && (
            <button
              className="cursor-pointer p-2 border-gray-400 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 transition"
              onClick={() => setShowAddRow(true)}
            >
              Add New Row
            </button>
          )}

          {!showAddRow && (
            <button
              className="cursor-pointer p-2 border-gray-400 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 transition"
              onClick={() => setShowAddColumn(true)}
            >
              Add New Column
            </button>
          )}
        </div>
      )}


      {showAddColumn && (
        <AddColumn
          diamondTypes={diamondTypes}
          onAdd={handleAddColumn}
          onCancel={() => setShowAddColumn(false)}
        />
      )}


    </div>
  );
}
