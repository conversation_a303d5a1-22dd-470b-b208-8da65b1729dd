import React, { useState } from "react";

export default function GemstonePriceTable({
  tableHeading,
  gemstonePrices = [],
  setGemstonePrices,
  isEditing,
  handlePriceChange,
  category,
  onAddRow,
}) {
  const [showAddRow, setShowAddRow] = useState(false);
  const [newRowData, setNewRowData] = useState({
    quality: "",
    ratePerCt: ""
  });
  const [validationErrors, setValidationErrors] = useState({});

  const handleNewRowDataChange = (field, value) => {
    // Validate ratePerCt field for negative values
    if (field === "ratePerCt") {
      const numValue = parseFloat(value);

      // Validate the value
      let hasError = false;
      if (value !== '' && (isNaN(numValue) || numValue < 0)) {
        hasError = true;
      }

      setValidationErrors(prev => ({
        ...prev,
        newRow: hasError
      }));
    }

    setNewRowData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddRow = async () => {
    if (!newRowData.quality || !newRowData.ratePerCt) {
      return;
    }

    // Check for validation errors
    if (validationErrors.newRow) {
      return;
    }

    const rowData = {
      quality: newRowData.quality.trim(),
      ratePerCt: parseFloat(newRowData.ratePerCt) || 0,
    };

    try {
      const response = await onAddRow(rowData); // API Call
      if (response?.data?.success) {
        setGemstonePrices((prev) => [...prev, response.data.newRow]);
        // Reset form
        setNewRowData({
          quality: "",
          ratePerCt: ""
        });
        setShowAddRow(false);
      }
    } catch (error) {
      console.error("Error adding row:", error);
    }
  };

  const handleCancelAddRow = () => {
    setNewRowData({
      quality: "",
      ratePerCt: ""
    });
    setShowAddRow(false);
  };
  
  
  return (
    <div className="overflow-x-auto flex flex-col items-center my-6">
      <table className="w-full min-w-[600px] divide-y divide-gray-300 border border-gray-300 shadow-md">
        <thead className="bg-gray-100">
          {tableHeading && (
            <tr>
              <th colSpan="2" className="px-6 py-4 text-center text-lg font-semibold bg-gray-300">
                {tableHeading}
              </th>
            </tr>
          )}
          <tr>
            <th className="px-6 py-3 text-center">Gemstone Quality</th>
            <th className="px-6 py-3 text-center bg-yellow-100">Price (₹)</th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-white text-center">
          {Array.isArray(gemstonePrices) &&
            gemstonePrices.map((item) => (
              <tr key={item._id}>
                <td className="p-4 text-center">{item.quality}</td>
                <td className="p-4 bg-yellow-50 text-center">
                  {isEditing ? (
                    <input
                      type="number"
                      className="w-20 p-2 border rounded-md text-center"
                      value={item.ratePerCt}
                      onChange={(e) => handlePriceChange(item._id, e.target.value, setGemstonePrices)}
                    />
                  ) : (
                    <span>₹{item.ratePerCt}</span>
                  )}
                </td>
              </tr>
            ))}

          {/* Add New Row Form within table */}
          {showAddRow && (
            <tr className="border-2 border-gray-300">
              <td className="p-4 text-center bg-gray-50">
                <input
                  type="text"
                  placeholder="Quality"
                  value={newRowData.quality}
                  onChange={(e) => handleNewRowDataChange("quality", e.target.value)}
                  className="w-32 p-2 border rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </td>
              <td className="p-4 text-center bg-yellow-50">
                <input
                  type="number"
                  placeholder="Price"
                  value={newRowData.ratePerCt}
                  onChange={(e) => handleNewRowDataChange("ratePerCt", e.target.value)}
                  className={`w-20 p-2 border rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    validationErrors.newRow ? 'border-red-500 bg-red-50' : 'border-gray-300'
                  }`}
                />
              </td>
            </tr>
          )}

          {/* Action buttons row when adding */}
          {showAddRow && (
            <tr className="bg-gray-50">
              <td colSpan="2" className="p-4 text-center">
                <div className="flex justify-center gap-2">
                  <button
                    onClick={handleAddRow}
                    className="px-4 py-2 bg-green-600 cursor-pointer text-white rounded-md hover:bg-green-700 transition"
                  >
                    Add Row
                  </button>
                  <button
                    onClick={handleCancelAddRow}
                    className="px-4 py-2 bg-red-600 cursor-pointer text-white rounded-md hover:bg-red-700 transition"
                  >
                    Cancel
                  </button>
                </div>
              </td>
            </tr>
          )}
        </tbody>
      </table>

      {/* Button to add a new row */}
      {!showAddRow && (
        <div className="flex justify-center mt-4">
          <button
            onClick={() => setShowAddRow(true)}
            className="cursor-pointer p-2 border-gray-400 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 transition"
          >
            + Add New Row
          </button>
        </div>
      )}
    </div>
  );
}
