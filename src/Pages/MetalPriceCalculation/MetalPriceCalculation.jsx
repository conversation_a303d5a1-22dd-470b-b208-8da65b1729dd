import React, { useEffect, useState } from "react";
import { useAuth } from "../../Context/AuthContext";
import { Bars3Icon } from "@heroicons/react/24/outline";
import ConfirmModal from "../../Components/Modal/ConfirmModal";
import Loader from "../../Components/Loader/Loader";
import ActionButton from "../../Components/Button/ActionButton";
import MessageNotification from "../../Components/MessageNotification/MessageNotification";
import Pagination from "../../Components/Pagination/Pagination";

import { getMetalPrices, updateHandlingCharges, addMetalPrice } from '../../Utils/MetalPriceCalculationApi'

export default function MetalPriceCalculation() {
  const {toggleSidebar, isDesktop } = useAuth();
  const [metalData, setMetalData] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [updatedCharges, setUpdatedCharges] = useState([]);
  const [loading, setLoading] = useState(false);
  const [buttonLoader, setButtonLoader] = useState(false);
  const [error, setError] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [previousData, setPreviousData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [showAddRow, setShowAddRow] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [newRowData, setNewRowData] = useState({
    metalName: "",
    metalPurity: "",
    handlingCharge: ""
  });
  const [dataUpdated, setDataUpdated] = useState(false);
  const itemsPerPage = 10;

  useEffect(() => {
    const fetchData = async () => {
        setLoading(true);
        try {
            const data = await getMetalPrices();
            setMetalData(data);
        } catch {
            console.error(error.response?.data || error.message || "Error Occured");
        } finally {
            setLoading(false);
        }
    };
    fetchData();
}, [dataUpdated]);

const handleNewRowDataChange = (field, value) => {
  // Validate handling charge field
  if (field === "handlingCharge") {
    // Handle empty string case
    if (value === '') {
      setValidationErrors(prev => ({
        ...prev,
        newRow: true // Empty values are invalid
      }));
      setNewRowData(prev => ({
        ...prev,
        [field]: ''
      }));
      return;
    }

    const numValue = parseFloat(value);

    // Validate the value
    let hasError = false;
    if (isNaN(numValue) || numValue < 0 || numValue > 100) {
      hasError = true;
    }

    setValidationErrors(prev => ({
      ...prev,
      newRow: hasError
    }));

    setNewRowData(prev => ({
      ...prev,
      [field]: isNaN(numValue) ? value : numValue
    }));
    return;
  }

  setNewRowData(prev => ({
    ...prev,
    [field]: value
  }));
};

const handleAddRow = async () => {
  if (!newRowData.metalName || !newRowData.metalPurity || !newRowData.handlingCharge) {
    setError("Please fill in all fields");
    setTimeout(() => setError(null), 5000);
    return;
  }

  // Check for validation errors
  if (validationErrors.newRow) {
    setError("Acceptable inputs is between 0 to 100");
    setTimeout(() => setError(null), 5000);
    return;
  }

  try {
    setLoading(true);
    setSuccessMessage(null);
    setError(null);

    const capitalizedMetalName = newRowData.metalName.charAt(0).toUpperCase() + newRowData.metalName.slice(1).toLowerCase();
    const uppercaseMetalPurity = newRowData.metalPurity.toUpperCase();

    const updatedData = {
      metalName: capitalizedMetalName,
      metalPurity: uppercaseMetalPurity,
      handlingCharge: parseFloat(newRowData.handlingCharge) || 0,
    };

    const response = await addMetalPrice(updatedData);

    setDataUpdated((prev) => !prev);

    // Reset form
    setNewRowData({
      metalName: "",
      metalPurity: "",
      handlingCharge: ""
    });
    setShowAddRow(false);

    setSuccessMessage(response?.message);
  } catch (errors) {
    console.error("API Error:", errors.response?.data || errors.message);
    handleError(errors);
  } finally {
    setLoading(false);
    setTimeout(() => {
      setSuccessMessage(null);
      setError(null);
    }, 5000);
  }
};

const handleCancelAddRow = () => {
  setNewRowData({
    metalName: "",
    metalPurity: "",
    handlingCharge: ""
  });
  setShowAddRow(false);
};

const handleError = (error) => {
  // Extract the message from the error response
  const errorMessage = error?.response?.data?.errors?.[0]?.message || error?.message || "Error Occurred";
  setError(errorMessage);  // Store the actual error message
  setTimeout(() => {
    setError(null); 
  }, 5000);
};

  const handleEditClick = () => {
    if (!isEditing) {
      setPreviousData([...metalData]);
    } else {
      setModalOpen(true);
    }
    setIsEditing(!isEditing);
  };

  const handleChargeChange = (id, value) => {
    // Handle empty string case
    if (value === '') {
      setValidationErrors(prev => ({
        ...prev,
        [id]: true // Empty values are invalid
      }));
      setMetalData((prevData) =>
        prevData.map((item) =>
          item._id === id
            ? { ...item, handlingCharge: '' }
            : item
        )
      );
      setUpdatedCharges((prev) => [
        ...prev.filter((item) => item.id !== id),
        { id, handlingCharge: '' },
      ]);
      return;
    }

    const numValue = parseFloat(value);

    // Validate the value
    let hasError = false;
    if (isNaN(numValue) || numValue < 0 || numValue > 100) {
      hasError = true;
    }

    setValidationErrors(prev => ({
      ...prev,
      [id]: hasError
    }));

    setMetalData((prevData) =>
      prevData.map((item) =>
        item._id === id
          ? { ...item, handlingCharge: isNaN(numValue) ? value : numValue }
          : item
      )
    );
    setUpdatedCharges((prev) => [
      ...prev.filter((item) => item.id !== id),
      { id, handlingCharge: isNaN(numValue) ? value : numValue },
    ]);
  };

  const handleConfirmChanges = async () => {
    // Check for validation errors before saving
    const hasErrors = Object.values(validationErrors).some(error => error);
    if (hasErrors) {
      setError("Acceptable inputs is between 0 to 100");
      setTimeout(() => setError(null), 5000);
      return;
    }

    setModalOpen(false);
    setButtonLoader(true);
    console.log("Final updatedCharges before API call: ", updatedCharges);

    try {
      const response = await updateHandlingCharges([...updatedCharges]);

      if (response) {
        setIsEditing(false);
        setUpdatedCharges([]);
        setValidationErrors({});
        setSuccessMessage("Handling charges updated successfully!");

        setTimeout(() => {
          setSuccessMessage("");
        }, 5000);
      } else {
        throw new Error("Failed to update!");
      }
    } catch (error) {
      setMetalData(previousData);
      setUpdatedCharges([]);
      handleError(error);
    } finally {
      setButtonLoader(false);
    }
  };
  

  const handleCancelChanges = () => {
    setMetalData(previousData);
    setUpdatedCharges([]);
    setValidationErrors({});
    setIsEditing(false);
    setModalOpen(false);
    setShowAddRow(false); // Close the add row state
  };

  const totalPages = Math.ceil(metalData.length / itemsPerPage);
  const currentItems = metalData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div className="flex lg:ml-72 flex-col">
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="w-full h-[100vh] bg-white shadow-sm p-6">
            <div className="flex justify-between items-center mb-6">
              <h1 className="font-bold text-gray-800" style={{ fontSize: '26px' }}>
                Metal Price Calculation
              </h1>
              <ActionButton
                onClick={handleEditClick}
                onDiscard={handleCancelChanges}
                isLoading={buttonLoader}
                isEditing={isEditing}
                text="Handling Charges"
              />
            </div>

            <MessageNotification message={successMessage} type="success" />
            {error && (
              <MessageNotification message={error} type="error" />
            )}

            <div className="overflow-x-auto pt-2 pb-8">
              <table className="min-w-full divide-y divide-gray-300 border border-gray-300 shadow-md">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-6 py-3 text-center">Metal</th>
                    <th className="px-6 py-3 text-center">Metal Purity</th>
                    <th className="px-6 py-3 text-center">Fetched Price</th>
                    <th className="px-6 py-3 text-center bg-yellow-100">
                      Handling Charge (%)
                    </th>
                    <th className="px-6 py-3 text-center">
                      Total Metal Cost (₹/gm)
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white text-center">
                  {currentItems.map((item, index) => {
                    const totalCost =
                      item.fetchedPrice * (1 + item.handlingCharge / 100);
                    return (
                      <tr key={item._id} className={index < currentItems.length - 1 ? "border-b border-gray-200" : ""}>
                        <td className="p-4 text-center">{item.metalName}</td>
                        <td className="p-4 text-center">{item.metalPurity}</td>
                        <td className="p-4 text-center">
                          ₹{item.fetchedPrice}
                        </td>
                        <td className="p-4 bg-yellow-50 text-center">
                          {isEditing ? (
                            <input
                              type="number"
                              className={`w-20 p-2 border rounded-md text-center ${
                                validationErrors[item._id] ? 'border-red-500 bg-red-50' : 'border-gray-300'
                              }`}
                              value={item.handlingCharge}
                              onChange={(e) =>
                                handleChargeChange(item._id, e.target.value)
                              }
                            />
                          ) : (
                            <span>{item.handlingCharge}%</span>
                          )}
                        </td>
                        <td className="p-4 text-center">
                          ₹{totalCost.toFixed(2)}
                        </td>
                      </tr>
                    );
                  })}

                  {/* Add New Row Form within table */}
                  {showAddRow && (
                    <tr className="border-2 border-gray-300">
                      <td className="p-4 text-center bg-white">
                        <input
                          type="text"
                          placeholder="Gold/Platinum"
                          value={newRowData.metalName}
                          onChange={(e) => handleNewRowDataChange("metalName", e.target.value)}
                          className="w-24 p-2 border rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </td>
                      <td className="p-4 text-center bg-white">
                        <input
                          type="text"
                          placeholder="Purity"
                          value={newRowData.metalPurity}
                          onChange={(e) => handleNewRowDataChange("metalPurity", e.target.value)}
                          className="w-20 p-2 border rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </td>
                      <td className="p-4 text-center bg-white">
                        <span className="text-gray-500">Auto-fetched</span>
                      </td>
                      <td className="p-4 text-center bg-yellow-50">
                        <input
                          type="number"
                          placeholder="Enter Handling Charge"
                          value={newRowData.handlingCharge}
                          onChange={(e) => handleNewRowDataChange("handlingCharge", e.target.value)}
                          className={`w-20 p-2 border rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                            validationErrors.newRow ? 'border-red-500 bg-red-50' : 'border-gray-300'
                          }`}
                        />
                      </td>
                      <td className="p-4 text-center bg-white">
                        <span className="text-gray-500">Calculated</span>
                      </td>
                    </tr>
                  )}

                  {/* Action buttons row when adding */}
                  {showAddRow && (
                    <tr className="bg-gray-50">
                      <td colSpan="5" className="p-4 text-center">
                        <div className="flex justify-center gap-2">
                          <button
                            onClick={handleAddRow}
                            className="px-4 py-2 bg-green-600 cursor-pointer text-white rounded-md hover:bg-green-700 transition"
                          >
                            Add Row
                          </button>
                          <button
                            onClick={handleCancelAddRow}
                            className="px-4 py-2 bg-red-600 cursor-pointer text-white rounded-md hover:bg-red-700 transition"
                          >
                            Cancel
                          </button>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {!showAddRow && (
              <div className="flex justify-center mt-6 pb-8">
                <button
                  className="cursor-pointer p-2 border-gray-400 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 transition"
                  onClick={() => setShowAddRow(true)}
                >
                  Add New Row
                </button>
              </div>
            )}

            {metalData.length > 10 && (
              <Pagination
                totalItems={metalData.length}
                itemsPerPage={itemsPerPage}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
              />
            )}
          </div>

          {modalOpen && (
        <ConfirmModal
          isOpen={modalOpen}
          onClose={() => setModalOpen(false)}
          onConfirm={handleConfirmChanges}
        />
      )}
        </>
      )}
    </div>
  );
}
