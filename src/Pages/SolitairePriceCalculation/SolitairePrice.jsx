import React, { useEffect, useState } from "react";
import { useAuth } from "../../Context/AuthContext";
import { Bars3Icon } from "@heroicons/react/24/outline";

import Loader from "../../Components/Loader/Loader";
import ActionButton from "../../Components/Button/ActionButton";
import MessageNotification from "../../Components/MessageNotification/MessageNotification";

import {
  roundShapeSolitairePrices,
  otherShapeSolitairePrices,
  roundShapeSolitaireConfig,
  otherShapeSolitaireConfig,
  updateConfigRound,
  updateConfigOther,
  updatePriceRound,
  updatePriceOther,
  addRoundShapeSolitaireRow,
  addOtherShapeSolitaireRow,
  addRoundShapeColumnSolitaire,
  addOtherShapeColumnSolitaire,
} from "../../Utils/SolitairePriceApi";
import AddColumn from "../../Components/EditTables/AddColumn";

export default function SolitairePrice() {
  const { toggleSidebar, isDesktop } = useAuth();
  const [solitaireData, setSolitaireData] = useState([]);
  const [discountRate, setDiscountRate] = useState(0);
  const [dollarRate, setDollarRate] = useState("");
  const [dhpcRate, setDhpcRate] = useState(0);
  const [isEditing, setIsEditing] = useState(false);

  const [updatedCharges, setUpdatedCharges] = useState([]);
  const [loading, setLoading] = useState(false);
  const [buttonLoader, setButtonLoader] = useState(false);
  const [error, setError] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [previousData, setPreviousData] = useState([]);
  const [selectedShape, setSelectedShape] = useState("round");
  const [shapeLoading, setShapeLoading] = useState(false);

  const [showAddRow, setShowAddRow] = useState(false);
  const [newRowData, setNewRowData] = useState({
    minCarat: "",
    maxCarat: "",
    prices: {}
  });
  const [isAddingColumn, setIsAddingColumn] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [configValidationErrors, setConfigValidationErrors] = useState({});

  const loadManualData = async (shape) => {
    setLoading(true);
    setError(false);
    try {
      const priceData =
        shape === "round"
          ? await roundShapeSolitairePrices()
          : await otherShapeSolitairePrices();

      if (priceData && priceData.data) {
        setSolitaireData(priceData.data);
      } else {
        throw new Error("Invalid price data structure for " + shape);
      }

      const configData =
        shape === "round"
          ? await roundShapeSolitaireConfig()
          : await otherShapeSolitaireConfig();

      if (configData && configData.data && configData.data.length > 0) {
        const config = configData.data[0];
        setDiscountRate(config.discountRate || 0);
        setDollarRate(config.todaysDollarPrice || "");
        setDhpcRate(config.dhplMarkup || 0);
      } else {
        throw new Error("Invalid configuration data structure for " + shape);
      }
    } catch (error) {
      console.error("Error loading data for", shape, ":", error.message);
      setError(true);
      setSuccessMessage("");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadManualData(selectedShape);
  }, [selectedShape]);

  const handleShapeChange = async (shape) => {
    if (shapeLoading || selectedShape === shape) return;
    setShapeLoading(true);
    setSelectedShape(shape);
    await loadManualData(shape);
    setShapeLoading(false);
  };

  const handleDiscountRateChange = (value) => {
    const numValue = parseFloat(value);

    // Validate the value
    let hasError = false;
    if (value !== '' && (isNaN(numValue) || numValue < 0)) {
      hasError = true;
    }

    setConfigValidationErrors(prev => ({
      ...prev,
      discountRate: hasError
    }));

    setDiscountRate(value);
  };

  const handleDhpcRateChange = (value) => {
    const numValue = parseFloat(value);

    // Validate the value
    let hasError = false;
    if (value !== '' && (isNaN(numValue) || numValue < 0)) {
      hasError = true;
    }

    setConfigValidationErrors(prev => ({
      ...prev,
      dhpcRate: hasError
    }));

    setDhpcRate(value);
  };

  const handleSaveChanges = async () => {
    // Check for validation errors before saving
    const hasErrors = Object.values(validationErrors).some(error => error);
    const hasConfigErrors = Object.values(configValidationErrors).some(error => error);

    if (hasErrors) {
      setError("Negative values are not allowed");
      setTimeout(() => setError(false), 5000);
      return;
    }

    if (hasConfigErrors) {
      setError("Discount Rate and DHPL Markup cannot be negative");
      setTimeout(() => setError(false), 5000);
      return;
    }

    setButtonLoader(true);
    setError(false);
    setSuccessMessage("");

    try {
      let successMsg = "";

      if (updatedCharges.length) {
        const updatePriceFn =
          selectedShape === "round" ? updatePriceRound : updatePriceOther;
        const priceResponse = await updatePriceFn(updatedCharges);
        successMsg =
          priceResponse?.data?.message || "Prices updated successfully!";
      }

      const updateConfigFn =
        selectedShape === "round" ? updateConfigRound : updateConfigOther;
      const configResponse = await updateConfigFn({
        discountRate,
        dhplMarkup: dhpcRate,
      });
      successMsg = configResponse?.data?.message || successMsg;

      setIsEditing(false);
      setUpdatedCharges([]);
      setSuccessMessage(successMsg);

      setTimeout(() => {
        setSuccessMessage("");
      }, 5000);
    } catch (error) {
      const errorMsg = error.response?.data?.message || "Error updating data!";
      setError(errorMsg);

      setTimeout(() => {
        setError(false);
      }, 5000);
    } finally {
      setButtonLoader(false);
    }
  };

  const handleEditClick = () => {
    if (!isEditing) {
      setPreviousData([...solitaireData]);
      setIsEditing(true);
    } else {
      handleSaveChanges();
    }
  };

  const handlePriceChange = (id, value) => {
    // Validate for negative values
    const numValue = parseFloat(value);

    // Validate the value
    let hasError = false;
    if (value !== '' && (isNaN(numValue) || numValue < 0)) {
      hasError = true;
    }

    setValidationErrors(prev => ({
      ...prev,
      [id]: hasError
    }));

    setSolitaireData((prevData) =>
      prevData.map((item) =>
        item._id === id ? { ...item, price: parseFloat(value) || 0 } : item
      )
    );
    setUpdatedCharges((prev) => [
      ...prev.filter((item) => item.id !== id),
      { id, price: parseFloat(value) || 0 },
    ]);
  };

  const gradeColumns = [...new Set(solitaireData.map((item) => item.grade))];

  const handleNewRowDataChange = (field, value) => {
    setNewRowData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNewRowPriceChange = (grade, value) => {
    // Validate for negative values
    const numValue = parseFloat(value);
    const key = `newRow_${grade}`;

    // Validate the value
    let hasError = false;
    if (value !== '' && (isNaN(numValue) || numValue < 0)) {
      hasError = true;
    }

    setValidationErrors(prev => ({
      ...prev,
      [key]: hasError
    }));

    setNewRowData(prev => ({
      ...prev,
      prices: {
        ...prev.prices,
        [grade]: value
      }
    }));
  };

  const handleAddRow = async () => {
    if (!newRowData.minCarat || !newRowData.maxCarat) {
      setError("Please enter both min and max carat values");
      setTimeout(() => setError(false), 5000);
      return;
    }

    // Check for validation errors in new row prices
    const newRowErrors = Object.keys(validationErrors).filter(key => key.startsWith('newRow_'));
    if (newRowErrors.some(key => validationErrors[key])) {
      setError("Negative values are not allowed");
      setTimeout(() => setError(false), 5000);
      return;
    }

    const formattedRows = gradeColumns.map((grade) => ({
      minCarat: parseFloat(newRowData.minCarat),
      maxCarat: parseFloat(newRowData.maxCarat),
      grade: grade,
      price: parseFloat(newRowData.prices[grade]) || 0,
    }));

    try {
      const addRowFn =
        selectedShape === "round"
          ? addRoundShapeSolitaireRow
          : addOtherShapeSolitaireRow;
      const response = await addRowFn(formattedRows);

      if (response && response.data) {
        setSolitaireData((prev) => [...prev, ...response.data.data]);
        // Reset form
        setNewRowData({
          minCarat: "",
          maxCarat: "",
          prices: gradeColumns.reduce((acc, grade) => ({ ...acc, [grade]: "" }), {})
        });
        setShowAddRow(false);
        setSuccessMessage(response.data.message || "Row added successfully!");

        setTimeout(() => {
          setSuccessMessage("");
        }, 5000);
      }
    } catch (error) {
      const errorMsg =
        error?.response?.data?.errors?.[0]?.message ||
        error?.message ||
        "Error adding row!";
      setError(errorMsg);

      setTimeout(() => {
        setError(false);
      }, 5000);
    }
  };

  const handleCancelAddRow = () => {
    setNewRowData({
      minCarat: "",
      maxCarat: "",
      prices: gradeColumns.reduce((acc, grade) => ({ ...acc, [grade]: "" }), {})
    });
    setShowAddRow(false);
  };

  // Refactored handleAddColumn function
  const handleAddColumnData = async (newColumnData) => {
    if (!newColumnData || !newColumnData.grade) return;

    setLoading(true);
    try {
      const addColumnFn =
        selectedShape === "round"
          ? addRoundShapeColumnSolitaire
          : addOtherShapeColumnSolitaire;

      const formattedPayload = newColumnData.data.map((row) => ({
        minCarat: row.minCarat,
        maxCarat: row.maxCarat,
        grade: newColumnData.grade.toUpperCase(),
        price: parseFloat(row.price) || 0,
      }));

      const response = await addColumnFn(formattedPayload);

      if (response?.data) {
        const updatedData =
          selectedShape === "round"
            ? await roundShapeSolitairePrices()
            : await otherShapeSolitairePrices();

        setSolitaireData(updatedData.data || []);

        setSuccessMessage(
          response.data.message || "Column added successfully!"
        );
        setTimeout(() => setSuccessMessage(""), 5000);
        setIsAddingColumn(false);
      }
    } catch (error) {
      const errorMsg =
        error.response?.data?.errors?.[0]?.message || "Error adding column!";
      setError(errorMsg);
      setTimeout(() => setError(""), 5000);
    } finally {
      setLoading(false);
    }
  };

  const existingGradeRows = Array.from(
    new Map(
      solitaireData.map((row) => [
        `${row.minCarat}-${row.maxCarat}`,
        { minCarat: row.minCarat, maxCarat: row.maxCarat },
      ])
    ).values()
  );

  return (
    <div className="flex lg:ml-72 flex-col">
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="w-full h-auto bg-white shadow-sm p-6">
            <div className="flex justify-between items-center mb-6">
              <h1 className="font-bold text-gray-800" style={{ fontSize: '26px' }}>
                Solitaire Price Calculation
              </h1>
              <ActionButton
                onClick={handleEditClick}
                onDiscard={() => {
                  setIsEditing(false);
                  setSolitaireData(previousData);
                  setUpdatedCharges([]);
                  setValidationErrors({});
                  setConfigValidationErrors({});
                  setError(false);
                }}
                isLoading={buttonLoader}
                isEditing={isEditing}
                text="Prices"
              />
            </div>

            <div className="mb-6 flex justify-center space-x-4">
              {["round", "other"].map((shape) => (
                <button
                  key={shape}
                  onClick={() => handleShapeChange(shape)}
                  disabled={shapeLoading}
                  className={`px-6 py-2 rounded-lg transition-colors duration-300 ${
                    shapeLoading
                      ? "bg-gray-400 text-gray-600 cursor-not-allowed"
                      : selectedShape === shape
                      ? "bg-gray-500 text-white cursor-pointer"
                      : "bg-gray-300 text-gray-500 hover:bg-gray-500 hover:text-white cursor-pointer"
                  }`}
                >
                  {shape === "round" ? "Round Shape" : "Other Shape"}
                </button>
              ))}
            </div>

            {shapeLoading && <Loader />}
            <MessageNotification message={successMessage} type="success" />
            {error && <MessageNotification message={error} type="error" />}

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-300 border border-gray-300 shadow-md">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-6 py-3 text-center">
                      Solitaire Ct (Min - Max)
                    </th>
                    {gradeColumns.map((grade) => (
                      <th key={grade} className="px-6 py-3 text-center">
                        {grade}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white text-center">
                  {[
                    ...new Set(
                      solitaireData.map(
                        (item) => `${item.minCarat}-${item.maxCarat}`
                      )
                    ),
                  ].map((range) => {
                    const filteredData = solitaireData.filter(
                      (item) => `${item.minCarat}-${item.maxCarat}` === range
                    );
                    return (
                      <tr key={range}>
                        <td className="p-4 text-center">{range}</td>
                        {gradeColumns.map((grade) => {
                          const item = filteredData.find(
                            (i) => i.grade === grade
                          );
                          return (
                            <td
                              key={grade}
                              className="p-4 text-center bg-yellow-100"
                            >
                              {isEditing ? (
                                <input
                                  type="number"
                                  value={item?.price || ""}
                                  onChange={(e) =>
                                    handlePriceChange(item?._id, e.target.value)
                                  }
                                  className={`w-20 p-2 border rounded-md text-center ${
                                    validationErrors[item?._id] ? 'border-red-500 bg-red-50' : 'border-gray-300'
                                  }`}
                                />
                              ) : (
                                `$${item?.price || "-"}`
                              )}
                            </td>
                          );
                        })}
                      </tr>
                    );
                  })}
                  {/* Add New Row Form within table */}
                  {showAddRow && (
                    <tr className="border-2 border-gray-300">
                      <td className="p-4 text-center bg-gray-50">
                        <div className="flex items-center gap-2 justify-center">
                          <input
                            type="number"
                            placeholder="Min"
                            value={newRowData.minCarat}
                            onChange={(e) => handleNewRowDataChange("minCarat", e.target.value)}
                            className="w-16 p-2 border rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                          <span>-</span>
                          <input
                            type="number"
                            placeholder="Max"
                            value={newRowData.maxCarat}
                            onChange={(e) => handleNewRowDataChange("maxCarat", e.target.value)}
                            className="w-16 p-2 border rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>
                      </td>
                      {gradeColumns.map((grade) => (
                        <td key={`new-${grade}`} className="p-4 text-center bg-yellow-100">
                          <input
                            type="number"
                            placeholder="Price"
                            value={newRowData.prices[grade] || ""}
                            onChange={(e) => handleNewRowPriceChange(grade, e.target.value)}
                            className={`w-20 p-2 border rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                              validationErrors[`newRow_${grade}`] ? 'border-red-500 bg-red-50' : 'border-gray-300'
                            }`}
                          />
                        </td>
                      ))}
                    </tr>
                  )}

                  {/* Action buttons row when adding */}
                  {showAddRow && (
                    <tr className="bg-gray-50">
                      <td colSpan={gradeColumns.length + 1} className="p-4 text-center">
                        <div className="flex justify-center gap-2">
                          <button
                            onClick={handleAddRow}
                            className="px-4 py-2 bg-green-600 cursor-pointer text-white rounded-md hover:bg-green-700 transition"
                          >
                            Add Row
                          </button>
                          <button
                            onClick={handleCancelAddRow}
                            className="px-4 py-2 bg-red-600 cursor-pointer text-white rounded-md hover:bg-red-700 transition"
                          >
                            Cancel
                          </button>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
              {isAddingColumn && (
                <AddColumn
                  isGradeMode={true}
                  onCancel={() => setIsAddingColumn(false)}
                  gradeRows={existingGradeRows}
                  onAdd={handleAddColumnData}
                />
              )}
              {/* Additional rows for Discount Rate, Dollar Rate, and DHPC Rate */}
              <div className="mt-8 w-auto rounded-lg overflow-hidden">
                <table className="w-auto md:w-100 border border-gray-300 shadow-lg">
                  <tbody className="divide-y divide-gray-300 bg-white text-center">
                    <tr className="border border-gray-300">
                      <td className="p-3 font-semibold bg-gray-100 text-left border-r border-gray-300">
                        Discount Rate
                      </td>
                      <td className="p-3 bg-yellow-100">
                        {isEditing ? (
                          <input
                            type="number"
                            value={discountRate}
                            onChange={(e) => handleDiscountRateChange(e.target.value)}
                            className={`w-20 p-2 border rounded-md text-center ${
                              configValidationErrors.discountRate ? 'border-red-500 bg-red-50' : 'border-gray-300'
                            }`}
                          />
                        ) : (
                          `${discountRate || "-"}%`
                        )}
                      </td>
                    </tr>
                    <tr className="border border-gray-300">
                      <td className="p-3 font-semibold bg-gray-100 text-left border-r border-gray-300">
                        Today's Dollar Price
                      </td>
                      <td className="p-3 bg-white">₹{dollarRate}</td>
                    </tr>
                    <tr className="border border-gray-300">
                      <td className="p-3 font-semibold bg-gray-100 text-left border-r border-gray-300">
                        DHPL Markup
                      </td>
                      <td className="p-3 bg-yellow-100">
                        {isEditing ? (
                          <input
                            type="number"
                            value={dhpcRate}
                            onChange={(e) => handleDhpcRateChange(e.target.value)}
                            className={`w-20 p-2 border rounded-md text-center ${
                              configValidationErrors.dhpcRate ? 'border-red-500 bg-red-50' : 'border-gray-300'
                            }`}
                          />
                        ) : (
                          `${dhpcRate || "-"}%`
                        )}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div className="flex justify-center gap-4 mt-6 pb-8">
                {!showAddRow && !isAddingColumn && (
                  <button
                    className="cursor-pointer p-2 border-gray-400 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 transition"
                    onClick={() => setShowAddRow(true)}
                  >
                    Add New Row
                  </button>
                )}
                {!showAddRow && (
                  <button
                    className="cursor-pointer p-2 border-gray-400 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 transition"
                    onClick={() => {
                      setIsAddingColumn(true);
                    }}
                  >
                    Add New Column
                  </button>
                )}
              </div>
            </div>
          </div>

        </>
      )}
    </div>
  );
}
